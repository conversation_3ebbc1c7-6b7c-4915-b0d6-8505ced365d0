[Desktop Entry]
Version=1.0
Type=Application
Name=cursor完全清理
Name[en]=Cursor Complete Clean
Comment=一键完全清理Cursor用户数据，恢复到初始安装状态
Comment[en]=One-click complete clean of Cursor user data, restore to initial installation state
GenericName=Cursor清理工具
GenericName[en]=Cursor Reset Tool
Icon=/home/<USER>/Desktop/ruanjian/cursor/cursor-reset.svg
Exec=bash -c "cd '/home/<USER>/Desktop/ruanjian/cursor' && python3 cursor-reset.py --cli --auto-confirm && read -p '清理完成！按回车键关闭窗口...' dummy"
Terminal=true
Categories=Utility;Development;System;
Keywords=cursor;clean;reset;clear;ide;development;清理;重置;
StartupNotify=true
StartupWMClass=cursor-reset 